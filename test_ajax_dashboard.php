<?php
// Simple test script to verify the ajax dashboard data changes
echo "Testing ajax dashboard data changes...\n\n";

// Test the sales revenue endpoint
$test_url = "http://localhost/cgadmin/ajax/ajax_dashobard_data.php?action=sales_revenue&from=2024-01-01&to=2024-01-31";

echo "Testing sales revenue endpoint:\n";
echo "URL: $test_url\n\n";

// You can uncomment the following lines to test with curl if needed:
/*
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $test_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

echo "Response: $response\n";
*/

echo "Changes made:\n";
echo "1. Updated get_sales_revenue_summary() to use multi-site logic like cron\n";
echo "2. Added get_multi_site_total_between() function that:\n";
echo "   - Connects to both Turmeaus and C.Gars databases\n";
echo "   - Categorizes sales by payment method (Wire, PayPal, Phone, Sage)\n";
echo "   - Disables wire transfers (set to 0) as per cron logic\n";
echo "   - Combines totals from both sites\n";
echo "3. Updated handle_sales_chart() to use the same multi-site logic\n";
echo "4. Added proper input escaping for security\n\n";

echo "The revenue values should now match those calculated in the cron file.\n";
?>
