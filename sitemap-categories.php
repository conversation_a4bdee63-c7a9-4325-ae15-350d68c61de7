<?php

/*
 * Include the application_top.php script
 */
include_once('includes/application_top.php');

/*
 * Send the XML content header
 */
header('Content-Type: application/xml; charset=utf-8');

/*
 * Echo the XML declaration
 */
echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n"; ?>

<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
<?php

/*
 * Define the uniform node function
 */
function GenerateNode($data){
    $content = "\t<url>\n";
    $content .= "\t\t<loc>" . htmlspecialchars($data['loc'], ENT_XML1, 'UTF-8') . "</loc>\n";
    $content .= "\t\t<lastmod>" . htmlspecialchars($data['lastmod'], ENT_XML1, 'UTF-8') . "</lastmod>\n";
    $content .= "\t\t<changefreq>" . htmlspecialchars($data['changefreq'], ENT_XML1, 'UTF-8') . "</changefreq>\n";
    $content .= "\t\t<priority>" . htmlspecialchars($data['priority'], ENT_XML1, 'UTF-8') . "</priority>\n";
    $content .= "\t</url>\n";
    return $content;
}

/*
 * Define the SQL for the categories query
 */
$sql = "SELECT c.categories_id as cID, c.date_added as category_date_added, c.last_modified as category_last_mod
        FROM " . TABLE_CATEGORIES . " c
        JOIN " . TABLE_CATEGORIES_TO_STORES . " c2s
        ON c.categories_id = c2s.categories_id
        WHERE c.categories_status IN (1,2,3,4) AND c2s.store_cg = '1' AND c.categories_id != '2970'
        ORDER BY c.parent_id ASC, c.sort_order ASC, c.categories_id ASC";

/*
 * Execute the query
 */
$query = tep_db_query($sql);

/*
 * If there are returned rows...
 */
if (tep_db_num_rows($query) > 0) {
    /*
     * Initialize the container
     */
    $container = array();

    /*
     * Loop query result and populate container
     */
    while ($result = tep_db_fetch_array($query)) {
        $container[$result['cID']] = max($result['category_date_added'], $result['category_last_mod']);
    }

    /*
     * Free the resource...could be large
     */
    tep_db_free_result($query);

    /*
     * Sort the container based on last mod date
     */
    arsort($container);

    /*
     * Loop the result set
     */
    $total = sizeof($container);
    $_total = $total;
    foreach ($container as $cID => $last_mod) {
        $location = tep_href_link(FILENAME_DEFAULT, 'cPath=' . $cID, 'NONSSL', false);
        $changefreq = 'weekly';
        $priority = max(number_format($_total / $total, 1, '.', ','), .1);
        $_total--;

        $node_data = array(
            'loc' => $location,
            'lastmod' => date("Y-m-d", strtotime($last_mod)),
            'changefreq' => $changefreq,
            'priority' => $priority
        );

        /*
         * Echo the generated node
         */
        echo GenerateNode($node_data);
    }
}

echo '</urlset>';

?>