<?php
/*
  $Id: index.php,v 1.19 2003/06/27 09:38:31 dgw_ Exp $

  osCommerce, Open Source E-Commerce Solutions
  http://www.oscommerce.com

  Copyright (c) 2003 osCommerce

  Released under the GNU General Public License
*/

  require('includes/application_top.php');
  require(DIR_WS_INCLUDES . 'template_top.php');

?>
<table border="0" width="100%" cellspacing="2" cellpadding="2">
  <tr>
    <td width="100%" valign="top">


<?php

$activate_update = 1;

if ($activate_update) {

  $sql = "
    SELECT COALESCE(SUM(ot.value), 0) AS total_value
    FROM orders_total ot
    JOIN orders o ON o.orders_id = ot.orders_id
    WHERE ot.class = 'ot_total'
      AND o.date_purchased >= '2024-11-01 00:00:00'
      AND o.date_purchased <  '2024-11-06 00:00:00'
  ";

  $result = tep_db_query($sql);
  $row    = tep_db_fetch_array($result);

  echo 'November 1st - 5th 2024: &pound;' . number_format((float)$row['total_value'], 2);

  $sql = "
    SELECT COALESCE(SUM(ot.value), 0) AS total_value
    FROM orders_total ot
    JOIN orders o ON o.orders_id = ot.orders_id
    WHERE ot.class = 'ot_total'
      AND o.date_purchased >= DATE_FORMAT(CURDATE(), '%Y-%m-01')
      AND o.date_purchased <  DATE_FORMAT(DATE_ADD(CURDATE(), INTERVAL 1 MONTH), '%Y-%m-01')
  ";

  $result = tep_db_query($sql);
  $row    = tep_db_fetch_array($result);

  echo '<br /><br />Current month: <strong>&pound;' . number_format((float)$row['total_value'], 2) . '</strong>';

}


?>
	</td>
  </tr>
</table>
<?php
  require(DIR_WS_INCLUDES . 'template_bottom.php');
  require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
