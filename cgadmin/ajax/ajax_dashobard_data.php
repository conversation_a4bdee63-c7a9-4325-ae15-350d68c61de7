<?php
header('Content-Type: text/html; charset=utf-8');

chdir('../');

require('includes/application_top.php');
// if (!tep_session_is_registered('admin')) {
//     http_response_code(403);
//     echo json_encode(['error' => 'Access denied']);
//     exit;
// }

header('Content-Type: application/json; charset=utf-8');

$actions = [
    'top_selling'     => 'handle_top_selling',
    'order_status'    => 'handle_order_status',
    'cgplus_active'   => 'handle_cgplus_active',
    'summary'         => 'handle_summary',
    'sales_revenue'   => 'handle_sales_revenue',
    'sales_chart' => 'handle_sales_chart'
];

$includedStatusIds = [
    1,  // Order Received
    2,  // Processing for DISPATCH
    4,  // Dispatched
    7,  // Tracking Details
    8,  // Courtesy Email sent
    11, // Partially shipped
    14, // Order Completed
    16, // Awaiting Stock (Processing)
    20, // Awaiting Stock
    23, // Client Confirmed Delivery
    24, // Customer Courtesy Done
    28, // Dispatched - Multiple Shipments
    51  // Click & Collect Shipped
];

$dispatchedStatusIds = [
    4,   // Dispatched
    11,  // Partially shipped
    28,  // Dispatched - Multiple Shipments
    51   // Click & Collect Shipped
];

$action = isset($_GET['action']) ? $_GET['action'] : '';
if (!isset($actions[$action])) {
    echo json_encode(['error' => 'Invalid action']);
    exit;
}

call_user_func($actions[$action]);

// ------------------- HANDLERS -------------------

function handle_top_selling() {
    // Read year and month parameters from GET
    $year  = isset($_GET['year']) ? (int)$_GET['year'] : (int)date('Y');
    $month = isset($_GET['month']) ? (int)$_GET['month'] : null;

    // Call the main function and return as JSON
    $data = get_top_selling_products($month, $year);
    echo json_encode(['data' => $data]);
}


function handle_order_status() {
    $data = get_order_status_summary();
    echo json_encode($data);
}

function handle_sales_revenue() {
    $from = isset($_GET['from']) ? $_GET['from'] : date('Y-m-01');
    $to = isset($_GET['to']) ? $_GET['to'] : date('Y-m-d');

    $data = get_sales_revenue_summary($from, $to);
    echo json_encode($data);
}

function handle_cgplus_active() {
    $data = get_cgplus_summary();
    echo json_encode($data);
}

function handle_summary() {
    $month = isset($_GET['month']) ? (int)$_GET['month'] : (int)date('n');
    $year  = isset($_GET['year']) ? (int)$_GET['year'] : (int)date('Y');

    echo json_encode([
        'order_status'    => get_order_status_summary(),
        'cgplus_active'   => get_cgplus_summary()
    ]);
}

// ------------------- CORE FUNCTIONS -------------------

function get_top_selling_products($month = null, $year) {
    // Determine the date range based on selected month and year
    if ($month !== null && $month >= 1 && $month <= 12) {
        $first_day = date('Y-m-01 00:00:00', strtotime("$year-$month-01"));
        $last_day  = date('Y-m-t 23:59:59', strtotime($first_day));
    } else {
        $first_day = "$year-01-01 00:00:00";
        $last_day  = "$year-12-31 23:59:59";
    }

    // maintenance_sales_stats.php tarzı hızlı sorgu - order status kontrolü yok
    $sql = "
        SELECT op.products_id, SUM(op.products_quantity) AS total_sold
        FROM orders_products op
        INNER JOIN orders o ON o.orders_id = op.orders_id
        WHERE o.date_purchased >= '$first_day'
          AND o.date_purchased < DATE_ADD('$last_day', INTERVAL 1 SECOND)
        GROUP BY op.products_id
        ORDER BY total_sold DESC
        LIMIT 20
    ";
    $res = tep_db_query($sql);
    $data = [];

    while ($row = tep_db_fetch_array($res)) {
        $data[] = $row;
    }

    // Get product names
    $names = [];
    if (!empty($data)) {
        $ids = implode(',', array_map('intval', array_column($data, 'products_id')));
        $res2 = tep_db_query("
            SELECT products_id, products_name
            FROM products_description
            WHERE products_id IN ($ids)
              AND language_id = 1
        ");

        while ($n = tep_db_fetch_array($res2)) {
            $names[$n['products_id']] = mb_convert_encoding($n['products_name'], 'UTF-8', 'ISO-8859-1');
        }
    }

    foreach ($data as &$item) {
        $item['products_name'] = isset($names[$item['products_id']]) ? $names[$item['products_id']] : 'Unknown';
    }

    return $data;
}



function get_order_status_summary() {
    global $dispatchedStatusIds;

    $startOfToday = date('Y-m-d 00:00:00');
    $endOfToday = date('Y-m-d 23:59:59');
    $dispatchedIn = implode(',', array_map('intval', $dispatchedStatusIds));

    $sql = "
        SELECT
            SUM(processing) AS processing,
            SUM(received) AS received,
            SUM(dispatched_today) AS dispatched_today
        FROM (
            SELECT
                SUM(CASE WHEN orders_status = 2 THEN 1 ELSE 0 END) AS processing,
                SUM(CASE WHEN orders_status = 1 THEN 1 ELSE 0 END) AS received,
                0 AS dispatched_today
            FROM orders
            UNION ALL
            SELECT
                0 AS processing,
                0 AS received,
                COUNT(*) AS dispatched_today
            FROM orders_status_history
            WHERE orders_status_id IN ($dispatchedIn)
              AND date_added BETWEEN '$startOfToday' AND '$endOfToday'
        ) AS combined
    ";

    $res = tep_db_query($sql);
    $row = tep_db_fetch_array($res);

    return [
        'processing'       => (int)($row['processing'] ?? 0),
        'received'         => (int)($row['received'] ?? 0),
        'dispatched_today' => (int)($row['dispatched_today'] ?? 0)
    ];
}


function get_cgplus_summary() {
    $result = [
        'bronze' => 0,
        'silver' => 0,
        'gold'   => 0
    ];

    $map = [
        1 => 'bronze',
        2 => 'silver',
        3 => 'gold'
    ];

    $res = tep_db_query("
        SELECT customers_cgars_plus_active, COUNT(*) AS total
        FROM customers
        WHERE customers_cgars_plus_active IN (1,2,3)
        GROUP BY customers_cgars_plus_active
    ");

    while ($row = tep_db_fetch_array($res)) {
        $key = $map[(int)$row['customers_cgars_plus_active']] ?? null;
        if ($key) {
            $result[$key] = (int)$row['total'];
        }
    }

    return $result;
}

function get_sales_revenue_summary($from, $to) {
    $from_date = tep_db_input($from . ' 00:00:00');
    $to_date = tep_db_input($to . ' 23:59:59');

    // maintenance_sales_stats.php tarzı sorgu - sadece ot_total ve orders tablosu
    function get_total_between_maintenance_style($start, $end) {
        $sql = "
            SELECT COALESCE(SUM(ot.value), 0) AS total_value
            FROM orders_total ot
            JOIN orders o ON o.orders_id = ot.orders_id
            WHERE ot.class = 'ot_total'
              AND o.date_purchased >= '$start'
              AND o.date_purchased < '$end'
        ";

        $result = tep_db_query($sql);
        $row = tep_db_fetch_array($result);
        return (float)$row['total_value'];
    }

    // Geçen sene aynı dönem için tarih hesaplama
    $from_last_year = date('Y-m-d H:i:s', strtotime('-1 year', strtotime($from_date)));
    $to_last_year = date('Y-m-d H:i:s', strtotime('-1 year', strtotime($to_date)));

    // Bu yıl başından şimdiye kadar
    $start_this_year = date('Y-01-01 00:00:00');
    $current_time = date('Y-m-d H:i:s');

    // Geçen yıl tamamı
    $start_last_year = date('Y-01-01 00:00:00', strtotime('-1 year'));
    $end_last_year = date('Y-01-01 00:00:00'); // Bu yılın başı = geçen yılın sonu

    return [
        'this_period' => get_total_between_maintenance_style($from_date, $to_date),
        'last_year_same_period' => get_total_between_maintenance_style($from_last_year, $to_last_year),
        'this_year' => get_total_between_maintenance_style($start_this_year, $current_time),
        'last_year' => get_total_between_maintenance_style($start_last_year, $end_last_year),
    ];
}


function handle_sales_chart() {
    $labels = [];
    $thisYearSales = [];
    $customerData = [];

    for ($i = 5; $i >= 0; $i--) {
        $date = strtotime("-$i months");
        $label = date('M', $date); // Jan, Feb, etc.
        $labels[] = $label;

        $startThisYear = date('Y-m-01 00:00:00', $date);
        $endThisYear = date('Y-m-t 23:59:59', $date);

        // maintenance_sales_stats.php tarzı sorgu - sadece ot_total ve orders
        $q1 = "
            SELECT COALESCE(SUM(ot.value), 0) AS total_value
            FROM orders_total ot
            JOIN orders o ON o.orders_id = ot.orders_id
            WHERE ot.class = 'ot_total'
              AND o.date_purchased >= '$startThisYear'
              AND o.date_purchased < DATE_ADD('$endThisYear', INTERVAL 1 SECOND)
        ";
        $r1 = tep_db_query($q1);
        $row1 = tep_db_fetch_array($r1);
        $thisYearSales[] = (float)$row1['total_value'];

        // New customers - maintenance_sales_stats.php tarzı hızlı sorgu
        $q3 = "
            SELECT COUNT(*) AS total
            FROM customers_info
            WHERE customers_info_date_account_created >= '$startThisYear'
              AND customers_info_date_account_created < DATE_ADD('$endThisYear', INTERVAL 1 SECOND)
        ";
        $r3 = tep_db_query($q3);
        $row3 = tep_db_fetch_array($r3);
        $customerData[] = (int)($row3['total'] ?? 0);
    }

    echo json_encode([
        'labels'         => $labels,
        'sales' => $thisYearSales,
        'customers'      => $customerData
    ]);
}
