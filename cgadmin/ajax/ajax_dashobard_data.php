<?php
header('Content-Type: text/html; charset=utf-8');

chdir('../');

require('includes/application_top.php');
// if (!tep_session_is_registered('admin')) {
//     http_response_code(403);
//     echo json_encode(['error' => 'Access denied']);
//     exit;
// }

header('Content-Type: application/json; charset=utf-8');

$actions = [
    'top_selling'     => 'handle_top_selling',
    'order_status'    => 'handle_order_status',
    'cgplus_active'   => 'handle_cgplus_active',
    'summary'         => 'handle_summary',
    'sales_revenue'   => 'handle_sales_revenue',
    'sales_chart' => 'handle_sales_chart'
];

$includedStatusIds = [
    1,  // Order Received
    2,  // Processing for DISPATCH
    4,  // Dispatched
    7,  // Tracking Details
    8,  // Courtesy Email sent
    11, // Partially shipped
    14, // Order Completed
    16, // Awaiting Stock (Processing)
    20, // Awaiting Stock
    23, // Client Confirmed Delivery
    24, // Customer Courtesy Done
    28, // Dispatched - Multiple Shipments
    51  // Click & Collect Shipped
];

$dispatchedStatusIds = [
    4,   // Dispatched
    11,  // Partially shipped
    28,  // Dispatched - Multiple Shipments
    51   // Click & Collect Shipped
];

$action = isset($_GET['action']) ? $_GET['action'] : '';
if (!isset($actions[$action])) {
    echo json_encode(['error' => 'Invalid action']);
    exit;
}

call_user_func($actions[$action]);

// ------------------- HANDLERS -------------------

function handle_top_selling() {
    // Read year and month parameters from GET
    $year  = isset($_GET['year']) ? (int)$_GET['year'] : (int)date('Y');
    $month = isset($_GET['month']) ? (int)$_GET['month'] : null;

    // Call the main function and return as JSON
    $data = get_top_selling_products($month, $year);
    echo json_encode(['data' => $data]);
}


function handle_order_status() {
    $data = get_order_status_summary();
    echo json_encode($data);
}

function handle_sales_revenue() {
    $from = isset($_GET['from']) ? $_GET['from'] : date('Y-m-01');
    $to = isset($_GET['to']) ? $_GET['to'] : date('Y-m-d');

    $data = get_sales_revenue_summary($from, $to);
    echo json_encode($data);
}

function handle_cgplus_active() {
    $data = get_cgplus_summary();
    echo json_encode($data);
}

function handle_summary() {
    $month = isset($_GET['month']) ? (int)$_GET['month'] : (int)date('n');
    $year  = isset($_GET['year']) ? (int)$_GET['year'] : (int)date('Y');

    echo json_encode([
        'order_status'    => get_order_status_summary(),
        'cgplus_active'   => get_cgplus_summary()
    ]);
}

// ------------------- CORE FUNCTIONS -------------------

function get_top_selling_products($month = null, $year) {
    // Define which order statuses count as 'completed/delivered'
    global $includedStatusIds;
    $statusIn = implode(',', array_map('intval', $includedStatusIds));

    // Determine the date range based on selected month and year
    if ($month !== null && $month >= 1 && $month <= 12) {
        $first_day = date('Y-m-01 00:00:00', strtotime("$year-$month-01"));
        $last_day  = date('Y-m-t 23:59:59', strtotime($first_day));
    } else {
        $first_day = "$year-01-01 00:00:00";
        $last_day  = "$year-12-31 23:59:59";
    }

    // Query top-selling products only from completed orders
    $sql = "
        SELECT op.products_id, SUM(op.products_quantity) AS total_sold
        FROM orders_products op
        INNER JOIN orders o ON o.orders_id = op.orders_id
        WHERE o.orders_status IN ($statusIn)
          AND o.date_purchased BETWEEN '$first_day' AND '$last_day'
        GROUP BY op.products_id
        ORDER BY total_sold DESC
        LIMIT 20
    ";
    $res = tep_db_query($sql);
    $data = [];

    while ($row = tep_db_fetch_array($res)) {
        $data[] = $row;
    }

    // Get product names
    $names = [];
    if (!empty($data)) {
        $ids = implode(',', array_map('intval', array_column($data, 'products_id')));
        $res2 = tep_db_query("
            SELECT products_id, products_name
            FROM products_description
            WHERE products_id IN ($ids)
              AND language_id = 1
        ");

        while ($n = tep_db_fetch_array($res2)) {
            $names[$n['products_id']] = mb_convert_encoding($n['products_name'], 'UTF-8', 'ISO-8859-1');
        }
    }

    foreach ($data as &$item) {
        $item['products_name'] = isset($names[$item['products_id']]) ? $names[$item['products_id']] : 'Unknown';
    }

    return $data;
}



function get_order_status_summary() {
    global $dispatchedStatusIds;

    $startOfToday = date('Y-m-d 00:00:00');
    $endOfToday = date('Y-m-d 23:59:59');
    $dispatchedIn = implode(',', array_map('intval', $dispatchedStatusIds));

    $sql = "
        SELECT
            SUM(processing) AS processing,
            SUM(received) AS received,
            SUM(dispatched_today) AS dispatched_today
        FROM (
            SELECT
                SUM(CASE WHEN orders_status = 2 THEN 1 ELSE 0 END) AS processing,
                SUM(CASE WHEN orders_status = 1 THEN 1 ELSE 0 END) AS received,
                0 AS dispatched_today
            FROM orders
            UNION ALL
            SELECT
                0 AS processing,
                0 AS received,
                COUNT(*) AS dispatched_today
            FROM orders_status_history
            WHERE orders_status_id IN ($dispatchedIn)
              AND date_added BETWEEN '$startOfToday' AND '$endOfToday'
        ) AS combined
    ";

    $res = tep_db_query($sql);
    $row = tep_db_fetch_array($res);

    return [
        'processing'       => (int)($row['processing'] ?? 0),
        'received'         => (int)($row['received'] ?? 0),
        'dispatched_today' => (int)($row['dispatched_today'] ?? 0)
    ];
}


function get_cgplus_summary() {
    $result = [
        'bronze' => 0,
        'silver' => 0,
        'gold'   => 0
    ];

    $map = [
        1 => 'bronze',
        2 => 'silver',
        3 => 'gold'
    ];

    $res = tep_db_query("
        SELECT customers_cgars_plus_active, COUNT(*) AS total
        FROM customers
        WHERE customers_cgars_plus_active IN (1,2,3)
        GROUP BY customers_cgars_plus_active
    ");

    while ($row = tep_db_fetch_array($res)) {
        $key = $map[(int)$row['customers_cgars_plus_active']] ?? null;
        if ($key) {
            $result[$key] = (int)$row['total'];
        }
    }

    return $result;
}

function get_multi_site_total_between($start, $end) {
    // Initialize totals
    $cgars_wire_sales = 0;
    $cgars_phone_sales = 0;
    $cgars_paypal_sales = 0;
    $cgars_sage_sales = 0;
    $turmeaus_wire_sales = 0;
    $turmeaus_paypal_sales = 0;
    $turmeaus_sage_sales = 0;

    // Get Turmeaus sales using existing database functions
    // Note: This would need to be implemented with proper multi-database support
    // For now, we'll use only C.Gars data as the existing functions don't support multiple databases

    // Get C.Gars sales using existing database functions
    $start_escaped = tep_db_input($start);
    $end_escaped = tep_db_input($end);

    $order_query = tep_db_query("SELECT o.orders_id, o.payment_method FROM orders o WHERE o.date_purchased BETWEEN '$start_escaped' AND '$end_escaped' ORDER BY o.orders_id");
    while ($order_row = tep_db_fetch_array($order_query)) {
        $order_id = (int)$order_row['orders_id'];
        $order_totals_query = tep_db_query("SELECT ot.value FROM orders_total ot WHERE ot.orders_id = $order_id AND class = 'ot_total'");
        while ($order_totals_row = tep_db_fetch_array($order_totals_query)) {
            if (strpos($order_row['payment_method'], 'Transfer') !== false) {
                $cgars_wire_sales += $order_totals_row['value'];
            } elseif (strpos($order_row['payment_method'], 'Phone') !== false) {
                $cgars_phone_sales += $order_totals_row['value'];
            } elseif (strpos($order_row['payment_method'], 'PayPal') !== false) {
                $cgars_paypal_sales += $order_totals_row['value'];
            } else { // its Sage
                $cgars_sage_sales += $order_totals_row['value'];
            }
        }
    }

    // Disable wire transfers as per cron logic
    $cgars_wire_sales = 0;
    $turmeaus_wire_sales = 0;

    // Calculate totals like in cron
    $cgars_sales = ($cgars_wire_sales + $cgars_phone_sales + $cgars_paypal_sales + $cgars_sage_sales);
    $turmeaus_sales = ($turmeaus_wire_sales + $turmeaus_paypal_sales + $turmeaus_sage_sales);
    $total_sales = ($cgars_sales + $turmeaus_sales);

    return (float)$total_sales;
}

function get_sales_revenue_summary($from, $to) {
    $from_date = tep_db_input($from . ' 00:00:00');
    $to_date = tep_db_input($to . ' 23:59:59');

    $from_last_year = date('Y-m-d H:i:s', strtotime('-1 year', strtotime($from_date)));
    $to_last_year = date('Y-m-d H:i:s', strtotime('-1 year', strtotime($to_date)));

    $start_this_year = date('Y-01-01 00:00:00');
    $start_last_year = date('Y-01-01 00:00:00', strtotime('-1 year'));
    $end_last_year = date('Y-12-31 23:59:59', strtotime('-1 year'));

    return [
        'this_period' => get_multi_site_total_between($from_date, $to_date),
        'last_year_same_period' => get_multi_site_total_between($from_last_year, $to_last_year),
        'this_year' => get_multi_site_total_between($start_this_year, date('Y-m-d 23:59:59')),
        'last_year' => get_multi_site_total_between($start_last_year, $end_last_year),
    ];
}


function handle_sales_chart() {
    $labels = [];
    $thisYearSales = [];
    $customerData = [];

    for ($i = 5; $i >= 0; $i--) {
        $date = strtotime("-$i months");
        $label = date('M', $date); // Jan, Feb, etc.
        $labels[] = $label;

        $startThisYear = date('Y-m-01 00:00:00', $date);
        $endThisYear = date('Y-m-t 23:59:59', $date);

        // This year's sales using multi-site logic like cron
        $thisYearSales[] = get_multi_site_total_between($startThisYear, $endThisYear);

        // New customers
        $q3 = "
            SELECT COUNT(*) AS total
            FROM customers_info
            WHERE customers_info_date_account_created BETWEEN '$startThisYear' AND '$endThisYear'
        ";
        $r3 = tep_db_query($q3);
        $row3 = tep_db_fetch_array($r3);
        $customerData[] = (int)($row3['total'] ?? 0);
    }

    echo json_encode([
        'labels'         => $labels,
        'sales' => $thisYearSales,
        'customers'      => $customerData
    ]);
}
