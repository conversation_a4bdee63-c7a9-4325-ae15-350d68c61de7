<?php

    date_default_timezone_set('Europe/London');

	$current_time = date("Y-m-d") . '_' . date("G-i");

    $time1 = strtotime("-1 week", time());
    $date1 = date("Y-m-d", $time1);

  function dblink1_input($string) {
    global $dblink1;
    return mysqli_real_escape_string($dblink1, $string);
  }

  function dblink2_input($string) {
    global $dblink2;
    return mysqli_real_escape_string($dblink2, $string);
  }

  // 1) Copy data from all sites into report table

    // Connect to Turmeaus

    // $dblink1_Host = "**************:3306"; // use your real host name
    $dblink1_Host = "**************:3306"; // use your real host name
    $dblink1_UserName = "turmeaus_user";   // use your real login user name
    $dblink1_Password = "VZR3KAhWCSSG9gb9Cvp7";   // use your real login password
    $dblink1_DataBaseName = "turmeaus_db"; // use your real database name

    $dblink1 = mysqli_connect( "$dblink1_Host", "$dblink1_UserName", "$dblink1_Password", "$dblink1_DataBaseName" );
    if(!$dblink1){ die("connection object 1 not created: ".mysqli_error($dblink1)); }
    if(mysqli_connect_errno()) { die("Connect failed: ".mysqli_connect_errno()." : ". mysqli_connect_error()); }
	mysqli_select_db($dblink1, $dblink1_DataBaseName);  // select database 1

    // Connect to C.Gars

    $dblink2_Host = "localhost"; // use your real host name
    $dblink2_UserName = "devcgars_person";   // use your real login user name
    $dblink2_Password = "_vb}pU&~I2#n";   // use your real login password
    $dblink2_DataBaseName = "devcgars_db"; // use your real database name

    $dblink2 = mysqli_connect( "$dblink2_Host", "$dblink2_UserName", "$dblink2_Password", "$dblink2_DataBaseName" );
    if(!$dblink2){ die("connection object 2 not created: ".mysqli_error($dblink2)); }
    if(mysqli_connect_errno()) { die("Connect failed: ".mysqli_connect_errno()." : ". mysqli_connect_error()); }
	mysqli_select_db($dblink2, $dblink2_DataBaseName);  // select database 2


	// get Turmeaus order statuses

     $orders_statuses = array();
     $orders_status_array = array();
     $orders_status_query = mysqli_query($dblink1,"select orders_status_id, orders_status_name from orders_status where select_flag = '1'");
    while ($orders_status = mysqli_fetch_array($orders_status_query, MYSQLI_ASSOC) ) {
     $orders_statuses[] = array('id' => $orders_status['orders_status_id'], 'text' => $orders_status['orders_status_name']);
     $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
    }


	// clear CG temp table

	mysqli_query($dblink2,"TRUNCATE temp_site_sales_report");

	// select from Turmeaus
	$site_id = 8;

	$order_query = mysqli_query($dblink1,"SELECT o.* FROM orders o WHERE DATE(o.date_purchased)>=DATE('" . $date1 . "') ORDER BY o.orders_id"); // select all content
    while ($order_row = mysqli_fetch_array($order_query, MYSQLI_ASSOC) ) {

    $order_products_query = mysqli_query($dblink1,"select op.* from orders_products op, products p where op.orders_id = " . $order_row['orders_id'] . " and op.products_id = p.products_id and (p.products_free_shipping = '3' OR p.products_order_type = '2')"); // select all content

    while ($order_products_row = mysqli_fetch_array($order_products_query, MYSQLI_ASSOC) ) {

     // insert into CG temp table

	 if($order_products_row['orders_products_cg_cost'] > 0) {
	   $orders_products_cg_cost = $order_products_row['orders_products_cg_cost'];
	   $orders_products_trade_price_discount = $order_products_row['orders_products_trade_price_discount'];
	 } else {
	   $product_query = mysqli_query($dblink2,"SELECT p.products_trade_price, p.products_trade_price_discount FROM products p WHERE p.products_id = '" . ($order_products_row['products_id'] - $site_id) . "'"); // get the info from CG
       $product_row = mysqli_fetch_array($product_query, MYSQLI_ASSOC);
	   $orders_products_cg_cost = $product_row['products_trade_price'];
	   $orders_products_trade_price_discount = $product_row['products_trade_price_discount'];
	 }

	 mysqli_query($dblink2,"INSERT INTO temp_site_sales_report (site_id, orders_id, date_purchased, orders_status, customers_name, products_id, products_name, products_price, final_price, products_quantity, orders_products_cg_cost, orders_products_trade_price_discount) VALUES ('" . $site_id . "', '" . dblink1_input($order_row['orders_id']) . "', '" . dblink1_input($order_row['date_purchased']) . "', '" . dblink1_input($orders_status_array[$order_row["orders_status"]]) . "', '" . dblink1_input($order_row['customers_name']) . "', '" . dblink1_input($order_products_row['products_id'] - $site_id) . "', '" . dblink1_input($order_products_row['products_name']) . "', '" . dblink1_input($order_products_row['products_price']) . "', '" . dblink1_input($order_products_row['final_price']) . "', '" . dblink1_input($order_products_row['products_quantity']) . "', '" . dblink1_input($orders_products_cg_cost) . "', '" . dblink1_input($orders_products_trade_price_discount) . "')");

    }

	}

    // Close Turmeaus Connection

    mysqli_close($dblink1);

    // Connect to Whisky Merchant

    $dblink1_Host = "**************:3306"; // use your real host name
    $dblink1_UserName = "whiskymerchant_user";   // use your real login user name
    $dblink1_Password = "^$0u(N+}ACc]";   // use your real login password
    $dblink1_DataBaseName = "whiskymerchant_db"; // use your real database name

    $dblink1 = mysqli_connect( "$dblink1_Host", "$dblink1_UserName", "$dblink1_Password", "$dblink1_DataBaseName" );
    if(!$dblink1){ die("connection object 1 not created: ".mysqli_error($dblink1)); }
    if(mysqli_connect_errno()) { die("Connect failed: ".mysqli_connect_errno()." : ". mysqli_connect_error()); }
	mysqli_select_db($dblink1, $dblink1_DataBaseName);  // select database 1

	// get Whisky Merchant order statuses

     $orders_statuses = array();
     $orders_status_array = array();
     $orders_status_query = mysqli_query($dblink1,"select orders_status_id, orders_status_name from orders_status where select_flag = '1'");
    while ($orders_status = mysqli_fetch_array($orders_status_query, MYSQLI_ASSOC) ) {
     $orders_statuses[] = array('id' => $orders_status['orders_status_id'], 'text' => $orders_status['orders_status_name']);
     $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
    }

	// select from Whisky Merchant
	$site_id = 6;

	$order_query = mysqli_query($dblink1,"SELECT o.* FROM orders o WHERE DATE(o.date_purchased)>=DATE('" . $date1 . "') ORDER BY o.orders_id"); // select all content
    while ($order_row = mysqli_fetch_array($order_query, MYSQLI_ASSOC) ) {

    $order_products_query = mysqli_query($dblink1,"select op.* from orders_products op, products p where op.orders_id = " . $order_row['orders_id'] . " and op.products_id = p.products_id and (p.products_free_shipping = '3' OR p.products_order_type = '2')"); // select all content

    while ($order_products_row = mysqli_fetch_array($order_products_query, MYSQLI_ASSOC) ) {

     // insert into CG temp table

	 if($order_products_row['orders_products_cg_cost'] > 0) {
	   $orders_products_cg_cost = $order_products_row['orders_products_cg_cost'];
	   $orders_products_trade_price_discount = $order_products_row['orders_products_trade_price_discount'];
	 } else {
	   $product_query = mysqli_query($dblink2,"SELECT p.products_trade_price, p.products_trade_price_discount FROM products p WHERE p.products_id = '" . ($order_products_row['products_id'] - $site_id) . "'"); // get the info from CG
       $product_row = mysqli_fetch_array($product_query, MYSQLI_ASSOC);
	   $orders_products_cg_cost = $product_row['products_trade_price'];
	   $orders_products_trade_price_discount = $product_row['products_trade_price_discount'];
	 }

	 mysqli_query($dblink2,"INSERT INTO temp_site_sales_report (site_id, orders_id, date_purchased, orders_status, customers_name, products_id, products_name, products_price, final_price, products_quantity, orders_products_cg_cost, orders_products_trade_price_discount) VALUES ('" . $site_id . "', '" . dblink1_input($order_row['orders_id']) . "', '" . dblink1_input($order_row['date_purchased']) . "', '" . dblink1_input($orders_status_array[$order_row["orders_status"]]) . "', '" . dblink1_input($order_row['customers_name']) . "', '" . dblink1_input($order_products_row['products_id'] - $site_id) . "', '" . dblink1_input($order_products_row['products_name']) . "', '" . dblink1_input($order_products_row['products_price']) . "', '" . dblink1_input($order_products_row['final_price']) . "', '" . dblink1_input($order_products_row['products_quantity']) . "', '" . dblink1_input($orders_products_cg_cost) . "', '" . dblink1_input($orders_products_trade_price_discount) . "')");

    }

	}

    // Close Whisky Merchant Connection

    mysqli_close($dblink1);

	// get Whisky Merchant order statuses



	// Get CG order statuses DBLINK2!!

	 $orders_statuses = array();
     $orders_status_array = array();
     $orders_status_query = mysqli_query($dblink2,"select orders_status_id, orders_status_name from orders_status where select_flag = '1'");
    while ($orders_status = mysqli_fetch_array($orders_status_query, MYSQLI_ASSOC) ) {
     $orders_statuses[] = array('id' => $orders_status['orders_status_id'], 'text' => $orders_status['orders_status_name']);
     $orders_status_array[$orders_status['orders_status_id']] = $orders_status['orders_status_name'];
    }

	// select from CGARS DBLINK2
	$site_id = 1;

	$order_query = mysqli_query($dblink2,"SELECT o.* FROM orders o WHERE DATE(o.date_purchased)>=DATE('" . $date1 . "') ORDER BY o.orders_id"); // select all content
    while ($order_row = mysqli_fetch_array($order_query, MYSQLI_ASSOC) ) {

    $order_products_query = mysqli_query($dblink2,"select op.* from orders_products op, products p where op.orders_id = " . $order_row['orders_id'] . " and op.products_id = p.products_id and (p.products_free_shipping = '3' OR p.products_order_type = '2')"); // select all content

    while ($order_products_row = mysqli_fetch_array($order_products_query, MYSQLI_ASSOC) ) {

     // insert into CG temp table

	 mysqli_query($dblink2,"INSERT INTO temp_site_sales_report (site_id, orders_id, date_purchased, orders_status, customers_name, products_id, products_name, products_price, final_price, products_quantity, orders_products_cg_cost, orders_products_trade_price_discount) VALUES ('" . $site_id . "', '" . dblink2_input($order_row['orders_id']) . "', '" . dblink2_input($order_row['date_purchased']) . "', '" . dblink2_input($orders_status_array[$order_row["orders_status"]]) . "', '" . dblink2_input($order_row['customers_name']) . "', '" . dblink2_input($order_products_row['products_id']) . "', '" . dblink2_input($order_products_row['products_name']) . "', '" . dblink2_input($order_products_row['products_price']) . "', '" . dblink2_input($order_products_row['final_price']) . "', '" . dblink2_input($order_products_row['products_quantity']) . "', '" . dblink2_input($order_products_row['orders_products_cg_cost']) . "', '" . dblink2_input($order_products_row['orders_products_trade_price_discount']) . "')");

    }

	}


	// Close CG Connection

    mysqli_close($dblink2);


  // START APPLICATION TOP

  chdir('/home/<USER>/public_html/cgadmin');

  $language = 'english';

// Set the level of error reporting
  error_reporting(E_ALL & ~E_NOTICE);

// Set the local configuration parameters - mainly for developers
  if (file_exists('includes/local/configure.php')) include('includes/local/configure.php');

// Include application configuration parameters
  require('includes/configure.php');

// some code to solve compatibility issues
  require(DIR_WS_FUNCTIONS . 'compatibility.php');

// include the list of project filenames
  require(DIR_WS_INCLUDES . 'filenames.php');

// include the list of project database tables
  require(DIR_WS_INCLUDES . 'database_tables.php');

// include the database functions
  require(DIR_WS_FUNCTIONS . 'database.php');

// make a connection to the database... now
  tep_db_connect() or die('Unable to connect to database server!');

// set application wide parameters
  $configuration_query = tep_db_query('select configuration_key as cfgKey, configuration_value as cfgValue from ' . TABLE_CONFIGURATION);
  while ($configuration = tep_db_fetch_array($configuration_query)) {
    define($configuration['cfgKey'], $configuration['cfgValue']);
  }

// define our general functions used application-wide
  require(DIR_WS_FUNCTIONS . 'general.php');
  require(DIR_WS_FUNCTIONS . 'html_output.php');

// define how the session functions will be used
  require(DIR_WS_FUNCTIONS . 'sessions.php');

// include the language translations
  require(DIR_WS_LANGUAGES . $language . '.php');

// define our localization functions
  require(DIR_WS_FUNCTIONS . 'localization.php');

// Include validation functions (right now only email address)
  require(DIR_WS_FUNCTIONS . 'validations.php');

// setup our boxes
  require(DIR_WS_CLASSES . 'table_block.php');
  require(DIR_WS_CLASSES . 'box.php');

// email classes
  require(DIR_WS_CLASSES . 'mime.php');
  require(DIR_WS_CLASSES . 'email.php');

// language
  include(DIR_WS_CLASSES . 'language.php');
  $lng = new language();
  $lng->set_language($language);
  $language = $lng->language['directory'];
  $languages_id = $lng->language['id'];

  require(DIR_WS_CLASSES . 'currencies.php');
  $currencies = new currencies();

// END APPLICATION TOP

	// **** Temp directory ****
    define ('EP_TEMP_DIRECTORY', DIR_FS_CATALOG . 'reports/');
    $export_filename = "Weekly_Alcohol_Sales_Report_" . $current_time;
    $filestring = ""; // this holds the csv file we want to download
	$ep_separator = ',';

// Header

    $filestring .= 'Site' . $ep_separator;
	$filestring .= 'Order ID' . $ep_separator;
	$filestring .= 'Date' . $ep_separator;
	$filestring .= 'Status' . $ep_separator;
	$filestring .= 'Customer' . $ep_separator;
	$filestring .= 'Product' . $ep_separator;
	$filestring .= 'Quantity' . $ep_separator;
	$filestring .= 'Total (inc vat)' . $ep_separator;
	$filestring .= 'Total (ex vat)' . $ep_separator;
	$filestring .= 'Total Cost (ex vat)' . $ep_separator;
	$filestring .= 'Margin %';

	$filestring .= "\n"; // end of row

// Rows

    $orders_id = 0;
	$quantity_total = 0;
	$total_price_inc_vat_total = 0;
	$total_price_ex_vat_total = 0;
	$cost_price_total = 0;

    $user_query = tep_db_query("SELECT * FROM temp_site_sales_report order by site_id, orders_id");
	while($row = tep_db_fetch_array($user_query)){

     $profit_margin = tep_calculate_profit_margin($row['final_price'], $row['orders_products_cg_cost'], $row['orders_products_trade_price_discount']);
     $products_name = str_replace(',','', $row['products_name']); // remove commas

	 if($row['orders_id'] != $orders_id){

	 if($row['site_id'] == '1') { $filestring .= 'Cgars' . $ep_separator; }
	 if($row['site_id'] == '6') { $filestring .= 'Whisky Merchant' . $ep_separator; }
	 if($row['site_id'] == '8') { $filestring .= 'Turmeaus' . $ep_separator; }

	 $filestring .= $row['orders_id'] . $ep_separator;
	 $filestring .= $row['date_purchased'] . $ep_separator;
	 $filestring .= $row['orders_status'] . $ep_separator;
	 $filestring .= $row['customers_name'] . $ep_separator;

	 } else {

	 $filestring .= $ep_separator . $ep_separator . $ep_separator . $ep_separator . $ep_separator;

	 }

	 if($row['orders_products_trade_price_discount'] > 0) {
	    	$trade_price_discount_amount = $row['orders_products_cg_cost'] * ($row['orders_products_trade_price_discount'] / 100);
			$trade_price_ex_vat_with_discount = $row['orders_products_cg_cost'] - $trade_price_discount_amount;
	 } else {
	    	$trade_price_ex_vat_with_discount = $row['orders_products_cg_cost'];
	 }

	 $filestring .= $products_name . $ep_separator;
	 $filestring .= $row['products_quantity'] . $ep_separator;
	 $filestring .= ($row['final_price'] * $row['products_quantity'])  . $ep_separator;
	 $filestring .= number_format((float)(($row['final_price'] / 1.2) * $row['products_quantity']), 2, '.', '') . $ep_separator;
	 $filestring .= ($trade_price_ex_vat_with_discount * $row['products_quantity']) . $ep_separator;
	 $filestring .= number_format((float)$profit_margin, 2, '.', '');

	 $filestring .= "\n"; // end of row

	 $quantity_total += $row['products_quantity'];
	 $total_price_inc_vat_total +=($row['final_price'] * $row['products_quantity']);
	 $total_price_ex_vat_total +=(($row['final_price'] / 1.2) * $row['products_quantity']);
	 $cost_price_total +=($trade_price_ex_vat_with_discount * $row['products_quantity']);

	 $orders_id = $row['orders_id'];

	}

	 $filestring .= $ep_separator . $ep_separator . $ep_separator . $ep_separator . $ep_separator . $ep_separator . $ep_separator . $ep_separator . $ep_separator . $ep_separator . $ep_separator . "\n";
	 $filestring .= $ep_separator . $ep_separator . $ep_separator . $ep_separator . $ep_separator . "Totals:" . $ep_separator . $quantity_total . $ep_separator . number_format((float)$total_price_inc_vat_total, 2, '.', '') . $ep_separator . number_format((float)$total_price_ex_vat_total, 2, '.', '') . $ep_separator . number_format((float)$cost_price_total, 2, '.', '') . "\n";

        //*******************************
        // PUT FILE IN TEMP DIR
        //*******************************

		$tmpfname = EP_TEMP_DIRECTORY . $export_filename . '.csv';
        $fp = fopen( $tmpfname, "w+");
        fwrite($fp, $filestring);
        fclose($fp);

      $email_generic_heading = 'Your weekly alcohol sales report is ready';
      $email_generic_sub_heading = '';

      // load email templates
      include('../' . DIR_WS_LANGUAGES . 'english/email_generic_template.php');

	  $download_url = 'https://www.cgarsltd.co.uk/reports/' . $export_filename . ((EP_EXCEL_SAFE_OUTPUT == true)?'.csv':'.txt');
    
    $email_message = '';
	  $email_message .= 'Your report file is ready to download at: <a href="' . $download_url . '">' . $download_url . '</a>';
	  $email_message .= '<br /><br /><strong>To open the spreadsheet:</strong><br />Either drag the file into excel or Right Click on the file and select "Open With" Excel.<br /><br />';

    $email_text  = EMAIL_GENERIC_TEMPLATE_START;
    $email_text .= EMAIL_GENERIC_TEMPLATE_HEADER;
    $email_text .= '<h4 style="color: #3e484d;margin-left: 0;margin-right: 0;margin-top: 20px;margin-bottom: 8px;padding: 0;font-weight: bold;font-size: 19px;line-height: 25px;">Dear Dominique,</h4><p class="mbe" style="font-family: Helvetica, Arial, sans-serif;font-size: 16px;line-height: 23px;color: #616161;mso-line-height-rule: exactly;display: block;margin-top: 0;margin-bottom: 0;">' . $email_message . '</p>';
    $email_text .= EMAIL_GENERIC_TEMPLATE_FOOTER;
    $email_text .= EMAIL_GENERIC_TEMPLATE_END;

    // rp disabled 20/03/2025 as no longer used, cron was:
    // 3	9	*	*	1
    // php /home/<USER>/public_html/cgadmin/cron_multi_site_alcohol_sales_report.php

    // tep_mail('Richard Passey', '<EMAIL>', 'Your Weekly Alcohol Sales Report is Ready', $email_text, 'Tech - C.Gars Ltd', '<EMAIL>');

	  //tep_mail('Laura Graham', '<EMAIL>', 'Your Weekly Alcohol Sales Report is Ready', $email_text, 'Tech - C.Gars Ltd', '<EMAIL>');
	  //tep_mail('Ron Morrison', '<EMAIL>', 'Your Weekly Alcohol Sales Report is Ready', $email_text, 'Tech - C.Gars Ltd', '<EMAIL>');
    //tep_mail('Raluca', '<EMAIL>', 'Your Weekly Alcohol Sales Report is Ready', $email_text, 'Tech - C.Gars Ltd', '<EMAIL>');

    // echo '<a href="https://www.cgarsltd.co.uk/reports/' . $export_filename . ((EP_EXCEL_SAFE_OUTPUT == true)?'.csv':'.txt') . '">Click Here to Download</a>';

	die();

// RP END DOWNLOAD

   require(DIR_WS_INCLUDES . 'application_bottom.php');
?>
