# Ajax Dashboard Data Revenue Calculation Updates

## Overview
Updated `cgadmin/ajax/ajax_dashobard_data.php` to match the revenue calculation logic used in `cgadmin/cron_multi_site_total_sales_report.php`.

## Key Changes Made

### 1. New Multi-Site Revenue Function
- Added `get_multi_site_total_between($start, $end)` function
- Replicates the exact logic from the cron file
- Connects to both Turmeaus and C.Gars databases
- Categorizes sales by payment method

### 2. Database Connections
- **Turmeaus Database**: `92.205.107.110:3306` / `turmeaus_db`
- **C.Gars Database**: Current local database connection
- Added proper connection handling and cleanup

### 3. Payment Method Categorization
- **Wire Transfers**: Disabled (set to 0) as per cron logic
- **PayPal**: Combined from both sites
- **Phone Sales**: C.Gars only
- **Sage**: Combined from both sites

### 4. Revenue Calculation Logic
```php
$cgars_sales = ($cgars_wire_sales + $cgars_phone_sales + $cgars_paypal_sales + $cgars_sage_sales);
$turmeaus_sales = ($turmeaus_wire_sales + $turmeaus_paypal_sales + $turmeaus_sage_sales);
$total_sales = ($cgars_sales + $turmeaus_sales);
```

### 5. Updated Functions
- `get_sales_revenue_summary()`: Now uses multi-site logic
- `handle_sales_chart()`: Updated to use same calculation method
- Added `dblink1_input()` helper for secure database queries

### 6. Security Improvements
- Added proper input escaping for all database queries
- Used parameterized queries where possible
- Proper connection cleanup

## Files Modified
- `cgadmin/ajax/ajax_dashobard_data.php` - Main changes
- `test_ajax_dashboard.php` - Test script (new)
- `CHANGES_SUMMARY.md` - This documentation (new)

## Expected Results
The revenue values returned by the ajax dashboard should now exactly match those calculated and sent in the daily email report by the cron job.

## Testing
Use the test script or call the ajax endpoint directly:
```
GET /cgadmin/ajax/ajax_dashobard_data.php?action=sales_revenue&from=2024-01-01&to=2024-01-31
```

The response should include:
- `this_period`: Revenue for specified date range
- `last_year_same_period`: Same period last year
- `this_year`: Year-to-date revenue
- `last_year`: Full previous year revenue
